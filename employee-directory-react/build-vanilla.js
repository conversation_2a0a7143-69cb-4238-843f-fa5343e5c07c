#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Building React app for vanilla JS deployment...');

// Build the React app
console.log('📦 Building React application...');
execSync('npm run build', { stdio: 'inherit' });

// Create vanilla directory structure
const vanillaDir = '../vanilla-build';
const distDir = './dist';

// Clean and create vanilla directory
if (fs.existsSync(vanillaDir)) {
  fs.rmSync(vanillaDir, { recursive: true });
}
fs.mkdirSync(vanillaDir, { recursive: true });

// Copy built files
console.log('📁 Copying built files...');
fs.cpSync(distDir, vanillaDir, { recursive: true });

// Copy original assets that might be needed
console.log('🖼️ Copying additional assets...');
if (fs.existsSync('./public/img')) {
  fs.cpSync('./public/img', path.join(vanillaDir, 'img'), { recursive: true });
}
if (fs.existsSync('./public/zamestnanci.json')) {
  fs.copyFileSync('./public/zamestnanci.json', path.join(vanillaDir, 'zamestnanci.json'));
}

// Create a simple server script for testing
const serverScript = `#!/usr/bin/env node
const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  let filePath = '.' + req.url;
  if (filePath === './') filePath = './index.html';
  
  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
  };
  
  const contentType = mimeTypes[extname] || 'application/octet-stream';
  
  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        res.writeHead(404);
        res.end('File not found');
      } else {
        res.writeHead(500);
        res.end('Server error: ' + error.code);
      }
    } else {
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(\`Server running at http://localhost:\${PORT}/\`);
});
`;

fs.writeFileSync(path.join(vanillaDir, 'server.js'), serverScript);

console.log('✅ Build complete!');
console.log(`📂 Vanilla JS files are in: ${vanillaDir}`);
console.log('🌐 To test the vanilla build:');
console.log(`   cd ${vanillaDir}`);
console.log('   node server.js');
console.log('   Open http://localhost:3000');
