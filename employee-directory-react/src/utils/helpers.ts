import { Employee, Department } from '../types/employee';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const removeDiacritics = (str: string): string => {
  return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
};

export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 9) {
    return `+420 ${cleaned.substring(0,3)} ${cleaned.substring(3,6)} ${cleaned.substring(6)}`;
  }
  return phone;
};

export const getDepartmentIcon = (department: string): string => {
  if (department.includes('elektřin')) return 'Zap';
  if (department.includes('plyn')) return 'Flame';
  if (department.includes('POZE')) return 'Leaf';
  if (department.includes('trhy')) return 'TrendingUp';
  if (department.includes('ICT')) return 'Laptop';
  if (department.includes('Financování')) return 'Calculator';
  if (department.includes('PAS')) return 'Briefcase';
  if (department.includes('Záruky')) return 'Award';
  if (department.includes('Veřejné')) return 'Scale';
  if (department.includes('Účetnictví')) return 'BookOpen';
  if (department.includes('Smluvní')) return 'FileText';
  if (department.includes('Správa')) return 'Building';
  if (department.includes('Kybernetická')) return 'Shield';
  if (department.includes('Rozvoj')) return 'BarChart';
  if (department.includes('Compliance')) return 'Balance';
  if (department.includes('Představenstvo')) return 'Users';
  return 'Building';
};

export const extractDepartments = (employees: Employee[]): Department[] => {
  const departmentMap = new Map<string, number>();
  
  employees.forEach(emp => {
    const depts = emp.oddeleni.split(',').map(d => d.trim());
    depts.forEach(dept => {
      departmentMap.set(dept, (departmentMap.get(dept) || 0) + 1);
    });
  });

  return Array.from(departmentMap.entries())
    .map(([name, count]) => ({
      name,
      count,
      icon: getDepartmentIcon(name),
    }))
    .sort((a, b) => a.name.localeCompare(b.name, 'cs'));
};

export const getEmployeeInitials = (name: string): string => {
  const parts = name.trim().split(' ');
  if (parts.length >= 2) {
    return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

export const isManager = (position: string): boolean => {
  const pos = position.toLowerCase();
  return pos.includes('vedoucí') || 
         pos.includes('předseda') || 
         pos.includes('místopředseda');
};

export const getWorkplace = (employee: Employee): string => {
  if (employee.left && employee.top) {
    return 'OTE, a.s. - Wenceslas Square 1567/2, Prague';
  }
  return 'Neuvedeno';
};

export const highlightMatch = (text: string, query: string): string => {
  if (!query) return text;
  
  const normalizedText = removeDiacritics(text.toLowerCase());
  const normalizedQuery = removeDiacritics(query.toLowerCase());
  const index = normalizedText.indexOf(normalizedQuery);
  
  if (index === -1) return text;
  
  const before = text.substring(0, index);
  const match = text.substring(index, index + query.length);
  const after = text.substring(index + query.length);
  
  return `${before}<mark class="bg-primary-200 dark:bg-primary-800 px-1 rounded">${match}</mark>${after}`;
};
