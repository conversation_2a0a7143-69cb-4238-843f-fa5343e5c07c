import { useQuery } from '@tanstack/react-query';
import { Employee } from '../types/employee';

const fetchEmployees = async (): Promise<Employee[]> => {
  const response = await fetch('/zamestnanci.json');
  if (!response.ok) {
    throw new Error('Failed to fetch employees');
  }
  return response.json();
};

export const useEmployees = () => {
  return useQuery({
    queryKey: ['employees'],
    queryFn: fetchEmployees,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};
