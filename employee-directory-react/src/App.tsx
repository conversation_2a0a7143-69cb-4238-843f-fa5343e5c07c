import React, { useEffect, useState } from 'react';
import type { Employee } from './types/employee';

const App: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load employees data
  useEffect(() => {
    const loadEmployees = async () => {
      try {
        const response = await fetch('/zamestnanci.json');
        if (!response.ok) throw new Error('Failed to fetch');
        const data = await response.json();
        setEmployees(data);
        setFilteredEmployees(data);
      } catch (err) {
        setError('Nepodařilo se načíst data zaměstnanců');
      } finally {
        setIsLoading(false);
      }
    };
    loadEmployees();
  }, []);

  // Filter employees
  useEffect(() => {
    let filtered = employees.filter(emp => {
      const matchesDepartment = selectedDepartment === 'all' ||
        emp.oddeleni.includes(selectedDepartment);
      const matchesSearch = !searchQuery ||
        emp.jmeno.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.pozice.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.oddeleni.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesDepartment && matchesSearch;
    });
    setFilteredEmployees(filtered);
  }, [employees, selectedDepartment, searchQuery]);

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>Načítání...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h1>Chyba při načítání dat</h1>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>
            Zkusit znovu
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh' }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        padding: '1rem 0'
      }}>
        <div className="container">
          <div className="flex items-center justify-between">
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
              Seznam zaměstnanců
            </h1>
            <div>
              <input
                type="text"
                placeholder="Vyhledat zaměstnance..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{
                  padding: '0.5rem',
                  border: '1px solid #ccc',
                  borderRadius: '0.375rem',
                  width: '300px'
                }}
              />
            </div>
            <div>
              Počet: {filteredEmployees.length}
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main style={{ padding: '2rem 0' }}>
        <div className="container">
          <div className="grid grid-cols-auto">
            {filteredEmployees.map((employee, index) => (
              <div
                key={employee.jmeno}
                className="bg-white shadow rounded p-6 animate-slide-up"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="text-center mb-4">
                  <img
                    src={employee.obrazek}
                    alt={employee.jmeno}
                    style={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      objectFit: 'cover',
                      margin: '0 auto 1rem'
                    }}
                    onError={(e) => {
                      e.currentTarget.src = '/img/no-person-photo.png';
                    }}
                  />
                  <h3 className="font-bold text-lg">{employee.jmeno}</h3>
                  <p className="text-sm" style={{ color: '#00add0' }}>
                    {employee.pozice}
                  </p>
                  <p className="text-sm text-gray-600">
                    {employee.oddeleni}
                  </p>
                </div>
                {employee.email && (
                  <div style={{ textAlign: 'center' }}>
                    <a
                      href={`mailto:${employee.email}`}
                      className="btn btn-primary"
                    >
                      Kontakt
                    </a>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
