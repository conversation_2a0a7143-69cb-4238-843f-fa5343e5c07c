import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEmployeeStore } from './store/employeeStore';
import { useEmployees } from './hooks/useEmployees';
import Header from './components/Header';
import DepartmentFilters from './components/DepartmentFilters';
import EmployeeGrid from './components/EmployeeGrid';
import EmployeeModal from './components/EmployeeModal';
import Footer from './components/Footer';
import BackToTop from './components/BackToTop';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const AppContent: React.FC = () => {
  const { data: employees, isLoading, error } = useEmployees();
  const { setEmployees, darkMode } = useEmployeeStore();

  useEffect(() => {
    if (employees) {
      setEmployees(employees);
    }
  }, [employees, setEmployees]);

  useEffect(() => {
    // Initialize dark mode from store
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Chyba při načítání dat
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Nepodařilo se načíst seznam zaměstnanců.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Zkusit znovu
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <Header />
      <DepartmentFilters />
      <main className="flex-1">
        <EmployeeGrid isLoading={isLoading} />
      </main>
      <Footer />
      <EmployeeModal />
      <BackToTop />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AppContent />
    </QueryClientProvider>
  );
};

export default App;
