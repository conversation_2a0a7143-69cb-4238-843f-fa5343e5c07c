import React from 'react';
import { Users } from 'lucide-react';
import * as LucideIcons from 'lucide-react';
import { useEmployeeStore } from '../store/employeeStore';
import { extractDepartments } from '../utils/helpers';
import { cn } from '../utils/helpers';

const DepartmentFilters: React.FC = () => {
  const { employees, selectedDepartment, setSelectedDepartment } = useEmployeeStore();
  
  const departments = extractDepartments(employees);
  const totalCount = employees.length;

  const getIcon = (iconName: string) => {
    const IconComponent = (LucideIcons as any)[iconName];
    return IconComponent ? <IconComponent className="h-4 w-4" /> : <Users className="h-4 w-4" />;
  };

  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-wrap gap-2">
          {/* All Departments Button */}
          <button
            onClick={() => setSelectedDepartment('all')}
            className={cn(
              "inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
              "border border-gray-200 dark:border-gray-600",
              selectedDepartment === 'all'
                ? "bg-primary-500 text-white border-primary-500 shadow-md"
                : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            )}
          >
            <Users className="h-4 w-4 mr-2" />
            Všechna oddělení
            <span className={cn(
              "ml-2 px-2 py-0.5 rounded-full text-xs font-medium",
              selectedDepartment === 'all'
                ? "bg-white/20 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
            )}>
              {totalCount}
            </span>
          </button>

          {/* Department Buttons */}
          {departments.map((department) => (
            <button
              key={department.name}
              onClick={() => setSelectedDepartment(department.name)}
              className={cn(
                "inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                "border border-gray-200 dark:border-gray-600",
                selectedDepartment === department.name
                  ? "bg-primary-500 text-white border-primary-500 shadow-md"
                  : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              )}
              title={`Zobrazit zaměstnance z oddělení ${department.name}`}
            >
              {getIcon(department.icon)}
              <span className="ml-2 truncate max-w-[200px]">
                {department.name}
              </span>
              <span className={cn(
                "ml-2 px-2 py-0.5 rounded-full text-xs font-medium",
                selectedDepartment === department.name
                  ? "bg-white/20 text-white"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
              )}>
                {department.count}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DepartmentFilters;
