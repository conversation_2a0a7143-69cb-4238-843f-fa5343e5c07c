import React, { useEffect, useState } from 'react';
import { X, Phone, Smartphone, Mail, Building, MapPin, MessageSquare } from 'lucide-react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { useEmployeeStore } from '../store/employeeStore';
import { formatPhoneNumber, isManager, getEmployeeInitials, getWorkplace } from '../utils/helpers';

const EmployeeModal: React.FC = () => {
  const { selectedEmployee, isModalOpen, setIsModalOpen, setSelectedEmployee } = useEmployeeStore();
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (selectedEmployee) {
      setImageError(false);
    }
  }, [selectedEmployee]);

  const closeModal = () => {
    setIsModalOpen(false);
    setTimeout(() => setSelectedEmployee(null), 300);
  };

  if (!selectedEmployee) return null;

  const manager = isManager(selectedEmployee.pozice);
  const workplace = getWorkplace(selectedEmployee);
  const hasMapLocation = selectedEmployee.left && selectedEmployee.top;

  return (
    <Transition appear show={isModalOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex justify-between items-start mb-6">
                  <div className="flex-1" />
                  <button
                    onClick={closeModal}
                    className="rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <X className="h-5 w-5 text-gray-500" />
                  </button>
                </div>

                {/* Profile Section */}
                <div className="text-center mb-6">
                  <div className="relative inline-block mb-4">
                    {!imageError ? (
                      <img
                        src={selectedEmployee.obrazek}
                        alt={selectedEmployee.jmeno}
                        onError={() => setImageError(true)}
                        className="w-24 h-24 rounded-full object-cover border-4 border-gray-100 dark:border-gray-700"
                      />
                    ) : (
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center border-4 border-gray-100 dark:border-gray-700">
                        <span className="text-white font-bold text-xl">
                          {getEmployeeInitials(selectedEmployee.jmeno)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center justify-center gap-2">
                      {selectedEmployee.jmeno}
                      {manager && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                          Vedení
                        </span>
                      )}
                    </h2>
                    <p className="text-primary-600 dark:text-primary-400 font-medium">
                      {selectedEmployee.pozice}
                    </p>
                    <p className="text-gray-600 dark:text-gray-400">
                      {selectedEmployee.oddeleni}
                    </p>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4 mb-6">
                  {/* Workplace */}
                  <div className="flex items-start space-x-3">
                    <Building className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Pracoviště</p>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-gray-600 dark:text-gray-400">{workplace}</p>
                        {hasMapLocation && (
                          <a
                            href={`/mapa.html?employee=${encodeURIComponent(selectedEmployee.jmeno)}&highlight=true`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                          >
                            <MapPin className="h-3 w-3 mr-1" />
                            Mapa
                          </a>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Phone */}
                  {selectedEmployee.telefon && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Telefon</p>
                        <a
                          href={`tel:${selectedEmployee.telefon}`}
                          className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                        >
                          {formatPhoneNumber(selectedEmployee.telefon)}
                        </a>
                      </div>
                    </div>
                  )}

                  {/* Mobile */}
                  {selectedEmployee.mobil && (
                    <div className="flex items-center space-x-3">
                      <Smartphone className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Mobil</p>
                        <a
                          href={`tel:${selectedEmployee.mobil}`}
                          className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                        >
                          {formatPhoneNumber(selectedEmployee.mobil)}
                        </a>
                      </div>
                    </div>
                  )}

                  {/* Email */}
                  {selectedEmployee.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                        <a
                          href={`mailto:${selectedEmployee.email}`}
                          className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 break-all"
                        >
                          {selectedEmployee.email}
                        </a>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                {selectedEmployee.email && (
                  <div className="flex flex-col sm:flex-row gap-3">
                    <a
                      href={`https://teams.microsoft.com/l/chat/0/0?users=${selectedEmployee.email}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Teams
                    </a>
                    <a
                      href={`mailto:${selectedEmployee.email}`}
                      className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Email
                    </a>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default EmployeeModal;
