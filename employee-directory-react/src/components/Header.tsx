import React from 'react';
import { Users, Map, Sun, Moon } from 'lucide-react';
import { useEmployeeStore } from '../store/employeeStore';
import SearchBar from './SearchBar';

const Header: React.FC = () => {
  const { filteredEmployees, darkMode, toggleDarkMode } = useEmployeeStore();

  const getEmployeeCountText = (count: number): string => {
    if (count === 0) return 'Žádn<PERSON> zaměstnanci';
    if (count === 1) return '1 zaměstnanec';
    if (count <= 4) return `${count} zaměstnanci`;
    return `${count} zaměstnanců`;
  };

  return (
    <header className="bg-white dark:bg-gray-900 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Left Section: Logo + Title */}
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <img 
                src="/img/logo1.svg" 
                alt="Logo OTE" 
                className="h-8 w-auto lg:h-10"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
            <h1 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white hidden sm:block">
              Seznam zaměstnanců
            </h1>
          </div>

          {/* Center Section: Search */}
          <div className="flex-1 max-w-lg mx-4 lg:mx-8">
            <SearchBar />
          </div>

          {/* Right Section: Info + Theme */}
          <div className="flex items-center space-x-4">
            {/* Employee Count */}
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
              <Users className="h-4 w-4" />
              <span className="font-medium">
                {getEmployeeCountText(filteredEmployees.length)}
              </span>
            </div>

            {/* Map Link */}
            <a
              href="/mapa.html"
              target="_blank"
              rel="noopener noreferrer"
              className="hidden lg:flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              title="Mapa rozmístění pracovišť"
            >
              <Map className="h-4 w-4" />
              <span>Mapa</span>
            </a>

            {/* Theme Toggle */}
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              title={darkMode ? 'Přepnout na světlý režim' : 'Přepnout na tmavý režim'}
            >
              {darkMode ? (
                <Sun className="h-5 w-5 text-yellow-500" />
              ) : (
                <Moon className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Employee Count */}
        <div className="md:hidden pb-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <Users className="h-4 w-4" />
            <span className="font-medium">
              {getEmployeeCountText(filteredEmployees.length)}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
