import React, { useState, useRef, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { useEmployeeStore } from '../store/employeeStore';
import { Employee } from '../types/employee';
import { removeDiacritics, highlightMatch } from '../utils/helpers';

const SearchBar: React.FC = () => {
  const { 
    employees, 
    searchQuery, 
    setSearchQuery, 
    setSelectedEmployee, 
    setIsModalOpen 
  } = useEmployeeStore();
  
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<Employee[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [placeholder, setPlaceholder] = useState('Vyhledat zaměstnance...');
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const placeholders = [
    "Vyhledat zaměstnance...",
    "Zadejte jméno nebo příjmení...",
    "Hledat podle pozice...",
    "Najít kolegu z oddělení...",
    "Například: Novák, vedoucí..."
  ];

  // Animated placeholder effect
  useEffect(() => {
    if (searchQuery || document.activeElement === inputRef.current) return;

    let currentIndex = 0;
    let charIndex = 0;
    let isDeleting = false;

    const animatePlaceholder = () => {
      const currentText = placeholders[currentIndex];
      
      if (!isDeleting && charIndex <= currentText.length) {
        setPlaceholder(currentText.substring(0, charIndex));
        charIndex++;
        setTimeout(animatePlaceholder, 80);
      } else if (isDeleting && charIndex >= 0) {
        setPlaceholder(currentText.substring(0, charIndex));
        charIndex--;
        setTimeout(animatePlaceholder, 40);
      } else if (!isDeleting) {
        setTimeout(() => {
          isDeleting = true;
          animatePlaceholder();
        }, 2000);
      } else {
        isDeleting = false;
        currentIndex = (currentIndex + 1) % placeholders.length;
        setTimeout(animatePlaceholder, 500);
      }
    };

    const timer = setTimeout(animatePlaceholder, 1000);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Generate suggestions
  useEffect(() => {
    if (!searchQuery || searchQuery.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const normalizedQuery = removeDiacritics(searchQuery.toLowerCase());
    const matches = employees.filter(emp => {
      const name = removeDiacritics(emp.jmeno.toLowerCase());
      const position = removeDiacritics(emp.pozice.toLowerCase());
      const department = removeDiacritics(emp.oddeleni.toLowerCase());
      
      return name.includes(normalizedQuery) ||
             position.includes(normalizedQuery) ||
             department.includes(normalizedQuery);
    }).slice(0, 5);

    setSuggestions(matches);
    setShowSuggestions(matches.length > 0);
    setSelectedIndex(-1);
  }, [searchQuery, employees]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, suggestions.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          selectEmployee(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const selectEmployee = (employee: Employee) => {
    setSearchQuery(employee.jmeno);
    setShowSuggestions(false);
    setSelectedEmployee(employee);
    setIsModalOpen(true);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleFocus = () => {
    setPlaceholder('Začněte psát...');
  };

  const handleBlur = () => {
    // Delay hiding suggestions to allow clicking
    setTimeout(() => {
      setShowSuggestions(false);
      if (!searchQuery) {
        setPlaceholder('Vyhledat zaměstnance...');
      }
    }, 200);
  };

  return (
    <div className="relative w-full">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={searchQuery}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
          autoComplete="off"
          spellCheck="false"
        />
        
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
        >
          {suggestions.map((employee, index) => (
            <div
              key={employee.jmeno}
              onClick={() => selectEmployee(employee)}
              className={`flex items-center p-3 cursor-pointer transition-colors ${
                index === selectedIndex
                  ? 'bg-primary-50 dark:bg-primary-900/20'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <img
                src={employee.obrazek}
                alt={employee.jmeno}
                className="w-10 h-10 rounded-full object-cover mr-3"
                onError={(e) => {
                  e.currentTarget.src = '/img/no-person-photo.png';
                }}
              />
              <div className="flex-1 min-w-0">
                <div 
                  className="font-medium text-gray-900 dark:text-white truncate"
                  dangerouslySetInnerHTML={{ 
                    __html: highlightMatch(employee.jmeno, searchQuery) 
                  }}
                />
                <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {employee.pozice} • {employee.oddeleni}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
