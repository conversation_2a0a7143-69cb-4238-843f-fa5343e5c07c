import React from 'react';
import { Search, Users } from 'lucide-react';
import { useEmployeeStore } from '../store/employeeStore';
import EmployeeCard from './EmployeeCard';
import LoadingSpinner from './LoadingSpinner';

interface EmployeeGridProps {
  isLoading?: boolean;
}

const EmployeeGrid: React.FC<EmployeeGridProps> = ({ isLoading = false }) => {
  const { filteredEmployees, searchQuery, selectedDepartment } = useEmployeeStore();

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600 dark:text-gray-400">Načítání zaměstnanců...</p>
      </div>
    );
  }

  if (filteredEmployees.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Žádní zaměstnanci k zobrazení
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          {searchQuery ? (
            <>
              Nenašli jsme žádné zaměstnance odpovídající vašemu hledání "{searchQuery}".
              Zkuste změnit vyhledávací dotaz nebo filtr oddělení.
            </>
          ) : selectedDepartment !== 'all' ? (
            <>
              V oddělení "{selectedDepartment}" nejsou žádní zaměstnanci.
              Zkuste vybrat jiné oddělení.
            </>
          ) : (
            'Zkuste změnit filtr nebo vyhledávací dotaz.'
          )}
        </p>
        {(searchQuery || selectedDepartment !== 'all') && (
          <button
            onClick={() => {
              const store = useEmployeeStore.getState();
              store.setSearchQuery('');
              store.setSelectedDepartment('all');
            }}
            className="mt-4 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Zobrazit všechny zaměstnance
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Results Summary */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <Users className="h-4 w-4" />
          <span>
            Zobrazeno {filteredEmployees.length} zaměstnanc
            {filteredEmployees.length === 1 ? '' : 
             filteredEmployees.length <= 4 ? 'i' : 'ů'}
            {searchQuery && ` pro "${searchQuery}"`}
            {selectedDepartment !== 'all' && ` v oddělení "${selectedDepartment}"`}
          </span>
        </div>
      </div>

      {/* Employee Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {filteredEmployees.map((employee, index) => (
          <EmployeeCard
            key={employee.jmeno}
            employee={employee}
            index={index}
          />
        ))}
      </div>

      {/* Load More Button (for future pagination) */}
      {filteredEmployees.length > 20 && (
        <div className="mt-12 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Zobrazeno {Math.min(20, filteredEmployees.length)} z {filteredEmployees.length} zaměstnanců
          </p>
        </div>
      )}
    </div>
  );
};

export default EmployeeGrid;
