import React, { useState } from 'react';
import { Mouse<PERSON>ointer } from 'lucide-react';
import { Employee } from '../types/employee';
import { useEmployeeStore } from '../store/employeeStore';
import { isManager, getEmployeeInitials, cn } from '../utils/helpers';

interface EmployeeCardProps {
  employee: Employee;
  index: number;
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({ employee, index }) => {
  const { setSelectedEmployee, setIsModalOpen } = useEmployeeStore();
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    setSelectedEmployee(employee);
    setIsModalOpen(true);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const nameParts = employee.jmeno.trim().split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';
  const manager = isManager(employee.pozice);

  return (
    <div
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "group relative bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl",
        "border border-gray-200 dark:border-gray-700 cursor-pointer",
        "transition-all duration-300 ease-out transform hover:-translate-y-1",
        "animate-slide-up"
      )}
      style={{
        animationDelay: `${index * 50}ms`,
        animationFillMode: 'both'
      }}
    >
      {/* Manager Badge */}
      {manager && (
        <div className="absolute top-3 right-3 z-10">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
            Vedení
          </span>
        </div>
      )}

      <div className="p-6">
        {/* Avatar */}
        <div className="flex justify-center mb-4">
          <div className="relative">
            {!imageError ? (
              <img
                src={employee.obrazek}
                alt={employee.jmeno}
                onError={handleImageError}
                className={cn(
                  "w-20 h-20 rounded-full object-cover border-4 border-gray-100 dark:border-gray-700",
                  "transition-transform duration-300",
                  isHovered && "transform rotate-12 scale-105"
                )}
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center border-4 border-gray-100 dark:border-gray-700">
                <span className="text-white font-bold text-lg">
                  {getEmployeeInitials(employee.jmeno)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Name */}
        <div className="text-center mb-3">
          <div className="font-semibold text-gray-900 dark:text-white text-lg leading-tight">
            {firstName}
          </div>
          <div className="font-semibold text-gray-900 dark:text-white text-lg leading-tight">
            {lastName}
          </div>
        </div>

        {/* Position */}
        <div className="text-center mb-2">
          <p className="text-sm font-medium text-primary-600 dark:text-primary-400">
            {employee.pozice}
          </p>
        </div>

        {/* Department */}
        <div className="text-center mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
            {employee.oddeleni}
          </p>
        </div>

        {/* Click Info */}
        <div className={cn(
          "flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400",
          "transition-opacity duration-200",
          isHovered ? "opacity-100" : "opacity-0"
        )}>
          <MousePointer className="h-3 w-3" />
          <span>Více informací zobrazíte kliknutím</span>
        </div>
      </div>

      {/* Hover Effect Overlay */}
      <div className={cn(
        "absolute inset-0 rounded-xl bg-gradient-to-t from-primary-500/5 to-transparent",
        "transition-opacity duration-300",
        isHovered ? "opacity-100" : "opacity-0"
      )} />
    </div>
  );
};

export default EmployeeCard;
