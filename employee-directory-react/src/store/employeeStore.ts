import { create } from 'zustand';
import { Employee } from '../types/employee';

interface EmployeeStore {
  employees: Employee[];
  filteredEmployees: Employee[];
  selectedDepartment: string;
  searchQuery: string;
  darkMode: boolean;
  selectedEmployee: Employee | null;
  isModalOpen: boolean;
  
  // Actions
  setEmployees: (employees: Employee[]) => void;
  setFilteredEmployees: (employees: Employee[]) => void;
  setSelectedDepartment: (department: string) => void;
  setSearchQuery: (query: string) => void;
  toggleDarkMode: () => void;
  setSelectedEmployee: (employee: Employee | null) => void;
  setIsModalOpen: (isOpen: boolean) => void;
  filterEmployees: () => void;
}

const removeDiacritics = (str: string): string => {
  return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
};

export const useEmployeeStore = create<EmployeeStore>()((set, get) => ({
      employees: [],
      filteredEmployees: [],
      selectedDepartment: 'all',
      searchQuery: '',
      darkMode: false,
      selectedEmployee: null,
      isModalOpen: false,

      setEmployees: (employees) => {
        const sortedEmployees = [...employees].sort((a, b) => {
          const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
          const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
          return nameA.localeCompare(nameB, 'cs');
        });
        set({ employees: sortedEmployees, filteredEmployees: sortedEmployees });
      },

      setFilteredEmployees: (employees) => set({ filteredEmployees: employees }),

      setSelectedDepartment: (department) => {
        set({ selectedDepartment: department });
        get().filterEmployees();
      },

      setSearchQuery: (query) => {
        set({ searchQuery: query });
        get().filterEmployees();
      },

      toggleDarkMode: () => {
        const newDarkMode = !get().darkMode;
        set({ darkMode: newDarkMode });
        if (newDarkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },

      setSelectedEmployee: (employee) => set({ selectedEmployee: employee }),

      setIsModalOpen: (isOpen) => set({ isModalOpen: isOpen }),

      filterEmployees: () => {
        const { employees, selectedDepartment, searchQuery } = get();
        
        let filtered = employees.filter(employee => {
          // Department filter
          const matchesDepartment = selectedDepartment === 'all' || 
            employee.oddeleni.split(',').map(d => d.trim()).includes(selectedDepartment);
          
          // Search filter
          const normalizedQuery = removeDiacritics(searchQuery.toUpperCase());
          const matchesSearch = !searchQuery || 
            removeDiacritics(employee.jmeno.toUpperCase()).includes(normalizedQuery) ||
            removeDiacritics(employee.pozice.toUpperCase()).includes(normalizedQuery) ||
            removeDiacritics(employee.oddeleni.toUpperCase()).includes(normalizedQuery);
          
          return matchesDepartment && matchesSearch;
        });

        // Sort filtered results
        filtered = filtered.sort((a, b) => {
          const nameA = removeDiacritics(a.jmeno.trim().toUpperCase());
          const nameB = removeDiacritics(b.jmeno.trim().toUpperCase());
          return nameA.localeCompare(nameB, 'cs');
        });

        set({ filteredEmployees: filtered });
      },
    }));
